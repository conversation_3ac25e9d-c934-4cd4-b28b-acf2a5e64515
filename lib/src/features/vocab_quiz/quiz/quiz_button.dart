// lib/src/features/vocab_quiz/widgets/quiz_button_widget.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_providers.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_creation_sheet.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:vocadex/src/services/analytics_service.dart';
import 'package:vocadex/src/features/analytics/providers/daily_metrics_provider.dart';
import 'package:vocadex/src/core/router/router_provider.dart';

/// A button that starts a new quiz
class StartQuizButton extends ConsumerWidget {
  final bool isPrimary;
  final double height;
  final double width;
  final EdgeInsets padding;
  final QuizMode mode;
  final String label;
  final IconData icon;

  const StartQuizButton({
    super.key,
    this.isPrimary = true,
    this.height = 56,
    this.width = double.infinity,
    this.padding = EdgeInsets.zero,
    this.mode = QuizMode.train,
    this.label = 'Take a Quiz',
    this.icon = Icons.quiz,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: padding,
      child: SizedBox(
        height: height,
        width: width,
        child: ElevatedButton.icon(
          onPressed: () => startQuiz(context, ref, mode),
          icon: Icon(icon),
          label: Text(label),
          style: ElevatedButton.styleFrom(
            backgroundColor:
                isPrimary ? Theme.of(context).primaryColor : AppColors.white,
            foregroundColor:
                isPrimary ? AppColors.white : Theme.of(context).primaryColor,
            elevation: isPrimary ? 2 : 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: isPrimary
                  ? BorderSide.none
                  : BorderSide(color: Theme.of(context).primaryColor),
            ),
          ),
        ),
      ),
    );
  }

  static Future<void> startQuiz(
      BuildContext context, WidgetRef ref, QuizMode mode) async {
    // Set the quiz mode in the provider
    ref.read(quizModeProvider.notifier).state = mode;

    // Capture router reference early to avoid disposal issues
    final router = ref.read(routerProvider);

    BuildContext? dialogContext;
    bool dialogShown = false;

    // Check diamonds before showing loading dialog
    final firebaseService = FirebaseService();
    final isPremium = ref.watch(subscriptionStateProvider);
    bool canProceed = true; // Changed from isPremium to always true for testing

    //TODO: Uncomment this when we have a way to handle diamonds
    // if (!isPremium) {
    //   // Try to decrement a diamond
    //   canProceed = await firebaseService.decrementDiamondIfFreeUser();
    //   if (!canProceed) {
    //     if (context.mounted) {
    //       ScaffoldMessenger.of(context).showSnackBar(
    //         const SnackBar(
    //           content: Text(
    //               'You have no diamonds left for today. Come back tomorrow or upgrade to Premium!'),
    //         ),
    //       );
    //     }
    //     return;
    //   }
    // }

    // Show loading dialog and capture dialogContext
    if (context.mounted) {
      dialogShown = true;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dContext) {
          dialogContext = dContext;
          return AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(mode == QuizMode.train
                    ? 'Creating Your Training Plan...'
                    : 'Setting Up Your Challenge...'),
              ],
            ),
          );
        },
      );
    }

    // Start generating quiz in parallel based on the mode
    Quiz? quiz;

    try {
      if (mode == QuizMode.train) {
        // Generate a training mode quiz
        final generateQuiz = ref.read(generateQuizProvider);
        debugPrint(
            '🔍 DEBUG: Calling generateQuiz from provider for training mode');
        quiz = await generateQuiz();
      } else {
        // Generate a challenge mode quiz
        final generateChallengeQuiz = ref.read(generateChallengeQuizProvider);
        debugPrint('🔍 DEBUG: Calling generateChallengeQuiz from provider');
        quiz = await generateChallengeQuiz();
      }

      if (quiz == null) {
        debugPrint('❌ ERROR: generateQuiz returned null');
      } else {
        debugPrint(
            '✅ DEBUG: Quiz generated successfully with ${quiz.questions.length} questions');

        // Log question types for debugging
        final questionTypes = <String>[];
        for (final question in quiz.questions) {
          if (question is SpellWordQuestion) {
            questionTypes.add(
                'SpellWord: ${question.correctWord}, options: ${question.options.length}');
          } else if (question is MatchDefinitionQuestion) {
            questionTypes.add('MatchDefinition');
          } else if (question is FillInBlankQuestion) {
            questionTypes.add('FillInBlank');
          } else if (question is TrueFalseQuestion) {
            questionTypes.add('TrueFalse');
          } else {
            questionTypes.add('Unknown');
          }
        }
        debugPrint('📋 DEBUG: Question types: $questionTypes');
      }
    } catch (e, stackTrace) {
      debugPrint('❌ ERROR: Exception during quiz generation: $e');
      debugPrint('❌ STACK TRACE: $stackTrace');
      quiz = null;
    }

    // Close the dialog using dialogContext
    if (dialogShown && dialogContext != null && dialogContext!.mounted) {
      Navigator.of(dialogContext!).pop();
      // Add a small delay to ensure the dialog is fully dismissed
      await Future.delayed(const Duration(milliseconds: 100));
    }

    // Now push or show toast safely
    debugPrint('🔍 DEBUG: About to check quiz and context for navigation');
    debugPrint('🔍 DEBUG: quiz is ${quiz != null ? 'not null' : 'null'}');
    debugPrint('🔍 DEBUG: context.mounted is ${context.mounted}');

    if (quiz != null) {
      debugPrint('✅ DEBUG: Quiz is not null, proceeding with navigation');

      // Track quiz start analytics (only if context is mounted for analytics)
      if (context.mounted) {
        try {
          final isPremium = ref.read(subscriptionStateProvider);
          final dailyMetrics = ref.read(dailyMetricsProvider);

          await AnalyticsService.instance.trackQuizAttempted(
            quizType: mode.toString().split('.').last,
            questionsCount: quiz.questions.length,
            isPremium: isPremium,
            dailyQuizzesCount: dailyMetrics.quizzesAttempted + 1,
          );

          // Update daily metrics
          await ref
              .read(dailyMetricsProvider.notifier)
              .incrementQuizzesAttempted();

          // Track daily active user
          await AnalyticsService.instance
              .track('Daily Active User - Quiz Started');
        } catch (analyticsError) {
          debugPrint('Analytics tracking error: $analyticsError');
        }
      } else {
        debugPrint('⚠️ DEBUG: Context unmounted, skipping analytics tracking');
      }

      debugPrint(
          '🚀 DEBUG: About to navigate to quiz screen using router provider');

      // Note: Skipping provider check since ref is disposed, but quiz generation was successful
      debugPrint('🔍 DEBUG: Using captured router for navigation');

      // Use captured router reference - this works regardless of context state
      try {
        debugPrint('🔍 DEBUG: Router instance: ${router.runtimeType}');
        debugPrint(
            '🔍 DEBUG: Current location before navigation: ${router.routerDelegate.currentConfiguration}');
        debugPrint('🔍 DEBUG: Attempting to navigate to: ${RouteNames.quiz}');

        // Use go() instead of pushNamed() for routes outside shell context
        router.go(RouteNames.quiz);
        debugPrint('✅ DEBUG: Navigation call completed using router.go()');

        // Add a small delay and check current location
        await Future.delayed(const Duration(milliseconds: 100));
        debugPrint(
            '🔍 DEBUG: Current location after navigation: ${router.routerDelegate.currentConfiguration}');
      } catch (navigationError) {
        debugPrint('❌ DEBUG: Router navigation error: $navigationError');
        debugPrint('❌ DEBUG: Error type: ${navigationError.runtimeType}');
        debugPrint('❌ DEBUG: Error details: ${navigationError.toString()}');

        // Fallback to context navigation if router fails
        if (context.mounted) {
          try {
            context.pushNamed(RouteNames.quiz);
            debugPrint('✅ DEBUG: Fallback context navigation completed');
          } catch (contextError) {
            debugPrint(
                '❌ DEBUG: Context navigation also failed: $contextError');
          }
        } else {
          debugPrint(
              '❌ DEBUG: Both router and context navigation failed - context unmounted');
        }
      }
    } else {
      debugPrint('❌ DEBUG: Quiz is null, showing error toast');
      // Show error toast if quiz generation failed
      if (context.mounted) {
        final errorMessage = ref.read(quizErrorProvider) ??
            'Failed to generate quiz. Please try again.';
        showFailureToast(
          context,
          title: 'Quiz Generation Failed',
          description: errorMessage,
        );
        debugPrint(
            '❌ DEBUG: Quiz generation failed, showing error toast: $errorMessage');
      }
    }
  }
}

/// A reusable button for quiz modes (Train/Challenge)
class QuizModeButton extends ConsumerWidget {
  final double height;
  final double width;
  final EdgeInsets padding;
  final String labelText;
  final IconData icon;
  final bool isPrimary;
  final VoidCallback? onTap;
  final QuizMode mode;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final TextStyle? textStyle;

  const QuizModeButton({
    super.key,
    this.height = 56,
    this.width = double.infinity,
    this.padding = EdgeInsets.zero,
    required this.labelText,
    required this.icon,
    bool? isPrimary,
    this.onTap,
    required this.mode,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.textStyle,
  }) : isPrimary = isPrimary ?? (mode == QuizMode.train);

  /// Factory constructor for Train Mode button
  factory QuizModeButton.train({
    Key? key,
    double height = 56,
    double width = double.infinity,
    EdgeInsets padding = EdgeInsets.zero,
    VoidCallback? onTap,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
  }) {
    return QuizModeButton(
      key: key,
      height: height,
      width: width,
      padding: padding,
      labelText: 'Train Mode',
      icon: Icons.school,
      isPrimary: true,
      onTap: onTap,
      mode: QuizMode.train,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
    );
  }

  /// Factory constructor for Challenge Mode button
  factory QuizModeButton.challenge({
    Key? key,
    double height = 56,
    double width = double.infinity,
    EdgeInsets padding = EdgeInsets.zero,
    VoidCallback? onTap,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
  }) {
    return QuizModeButton(
      key: key,
      height: height,
      width: width,
      padding: padding,
      labelText: 'Challenge Mode',
      icon: Icons.timer,
      isPrimary: false,
      onTap: onTap,
      mode: QuizMode.challenge,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Padding(
      padding: padding,
      child: SizedBox(
        height: height,
        width: width,
        child: ElevatedButton.icon(
          onPressed: onTap ?? () => showQuizCreationSheet(context, ref, mode),
          icon: Icon(icon),
          label: Text(
            labelText,
            style: textStyle ?? const TextStyle(fontSize: 16),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ??
                (isPrimary ? theme.primaryColor : Colors.transparent),
            foregroundColor: foregroundColor ??
                (isPrimary ? AppColors.white : theme.primaryColor),
            elevation: isPrimary ? 2 : 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: borderColor != null
                  ? BorderSide(color: borderColor!)
                  : isPrimary
                      ? BorderSide.none
                      : BorderSide(color: theme.primaryColor),
            ),
          ),
        ),
      ),
    );
  }
}

// Kept for backward compatibility
class TrainModeButton extends ConsumerWidget {
  final double height;
  final double width;
  final EdgeInsets padding;
  final VoidCallback? onTap;

  const TrainModeButton({
    super.key,
    this.height = 56,
    this.width = double.infinity,
    this.padding = EdgeInsets.zero,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return QuizModeButton.train(
      key: key,
      height: height,
      width: width,
      padding: padding,
      onTap: onTap,
    );
  }
}

// Kept for backward compatibility
class ChallengeModeButton extends ConsumerWidget {
  final double height;
  final double width;
  final EdgeInsets padding;
  final VoidCallback? onTap;

  const ChallengeModeButton({
    super.key,
    this.height = 56,
    this.width = double.infinity,
    this.padding = EdgeInsets.zero,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return QuizModeButton.challenge(
      key: key,
      height: height,
      width: width,
      padding: padding,
      onTap: onTap,
    );
  }
}
